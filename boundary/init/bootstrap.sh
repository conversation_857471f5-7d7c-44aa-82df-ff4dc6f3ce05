#!/bin/bash

# Boundary Bootstrap Script
# This script initializes Boundary with basic configuration

set -e

echo "🎯 Bootstrapping HashiCorp Boundary..."

# Set Boundary address
export BOUNDARY_ADDR="http://localhost:9200"

# Wait for Boundary to be ready
echo "⏳ Waiting for Boundary controller to be ready..."
until curl -s http://localhost:9200/v1/scopes >/dev/null 2>&1; do
    echo "Waiting for Boundary controller..."
    sleep 5
done

echo "✅ Boundary controller is ready!"

# Authenticate as admin
echo "🔐 Authenticating as admin..."
boundary authenticate password \
    -auth-method-id=ampw_1234567890 \
    -login-name=admin \
    -password=password

# Get the admin token
BOUNDARY_TOKEN=$(boundary authenticate password \
    -auth-method-id=ampw_1234567890 \
    -login-name=admin \
    -password=password \
    -format=json | jq -r '.token')

export BOUNDARY_TOKEN

echo "✅ Authenticated successfully!"

# Create an organization scope
echo "🏢 Creating organization scope..."
ORG_ID=$(boundary scopes create \
    -scope-id=global \
    -name="demo-org" \
    -description="Demo organization for testing" \
    -format=json | jq -r '.id')

echo "Organization ID: $ORG_ID"

# Create a project scope
echo "📁 Creating project scope..."
PROJECT_ID=$(boundary scopes create \
    -scope-id="$ORG_ID" \
    -name="demo-project" \
    -description="Demo project for testing" \
    -format=json | jq -r '.id')

echo "Project ID: $PROJECT_ID"

# Create a host catalog
echo "🖥️  Creating host catalog..."
HOST_CATALOG_ID=$(boundary host-catalogs create static \
    -scope-id="$PROJECT_ID" \
    -name="demo-hosts" \
    -description="Demo host catalog" \
    -format=json | jq -r '.id')

echo "Host Catalog ID: $HOST_CATALOG_ID"

# Create a host set
echo "📋 Creating host set..."
HOST_SET_ID=$(boundary host-sets create static \
    -host-catalog-id="$HOST_CATALOG_ID" \
    -name="demo-host-set" \
    -description="Demo host set" \
    -format=json | jq -r '.id')

echo "Host Set ID: $HOST_SET_ID"

# Create hosts
echo "🖥️  Creating hosts..."

# PostgreSQL host
POSTGRES_HOST_ID=$(boundary hosts create static \
    -host-catalog-id="$HOST_CATALOG_ID" \
    -name="postgres-db" \
    -description="PostgreSQL Database" \
    -address="postgres" \
    -format=json | jq -r '.id')

echo "PostgreSQL Host ID: $POSTGRES_HOST_ID"

# Vault host
VAULT_HOST_ID=$(boundary hosts create static \
    -host-catalog-id="$HOST_CATALOG_ID" \
    -name="vault-server" \
    -description="HashiCorp Vault Server" \
    -address="vault" \
    -format=json | jq -r '.id')

echo "Vault Host ID: $VAULT_HOST_ID"

# Add hosts to host set
echo "🔗 Adding hosts to host set..."
boundary host-sets add-hosts \
    -id="$HOST_SET_ID" \
    -host="$POSTGRES_HOST_ID"

boundary host-sets add-hosts \
    -id="$HOST_SET_ID" \
    -host="$VAULT_HOST_ID"

# Create credential store
echo "🔑 Creating credential store..."
CRED_STORE_ID=$(boundary credential-stores create vault \
    -scope-id="$PROJECT_ID" \
    -name="demo-vault-store" \
    -description="Demo Vault credential store" \
    -vault-address="http://vault:8200" \
    -vault-token="myroot" \
    -format=json | jq -r '.id')

echo "Credential Store ID: $CRED_STORE_ID"

# Create targets
echo "🎯 Creating targets..."

# PostgreSQL target
POSTGRES_TARGET_ID=$(boundary targets create tcp \
    -scope-id="$PROJECT_ID" \
    -name="postgres-target" \
    -description="PostgreSQL Database Target" \
    -default-port=5432 \
    -session-connection-limit=-1 \
    -format=json | jq -r '.id')

echo "PostgreSQL Target ID: $POSTGRES_TARGET_ID"

# Vault target
VAULT_TARGET_ID=$(boundary targets create tcp \
    -scope-id="$PROJECT_ID" \
    -name="vault-target" \
    -description="HashiCorp Vault Target" \
    -default-port=8200 \
    -session-connection-limit=-1 \
    -format=json | jq -r '.id')

echo "Vault Target ID: $VAULT_TARGET_ID"

# Add host sets to targets
echo "🔗 Adding host sets to targets..."
boundary targets add-host-sources \
    -id="$POSTGRES_TARGET_ID" \
    -host-source="$HOST_SET_ID"

boundary targets add-host-sources \
    -id="$VAULT_TARGET_ID" \
    -host-source="$HOST_SET_ID"

# Create users
echo "👤 Creating users..."
USER_ID=$(boundary users create \
    -scope-id="$ORG_ID" \
    -name="demo-user" \
    -description="Demo user for testing" \
    -format=json | jq -r '.id')

echo "User ID: $USER_ID"

# Create groups
echo "👥 Creating groups..."
GROUP_ID=$(boundary groups create \
    -scope-id="$ORG_ID" \
    -name="demo-group" \
    -description="Demo group for testing" \
    -format=json | jq -r '.id')

echo "Group ID: $GROUP_ID"

# Add user to group
echo "🔗 Adding user to group..."
boundary groups add-members \
    -id="$GROUP_ID" \
    -member="$USER_ID"

# Create roles
echo "🛡️  Creating roles..."
ROLE_ID=$(boundary roles create \
    -scope-id="$PROJECT_ID" \
    -name="demo-role" \
    -description="Demo role for testing" \
    -format=json | jq -r '.id')

echo "Role ID: $ROLE_ID"

# Grant permissions to role
echo "🔐 Granting permissions to role..."
boundary roles add-grants \
    -id="$ROLE_ID" \
    -grant="id=*;type=target;actions=authorize-session"

boundary roles add-grants \
    -id="$ROLE_ID" \
    -grant="id=*;type=session;actions=read:self,cancel:self"

# Add principals to role
echo "🔗 Adding principals to role..."
boundary roles add-principals \
    -id="$ROLE_ID" \
    -principal="$GROUP_ID"

echo "🎉 Boundary bootstrap complete!"
echo ""
echo "📋 Summary:"
echo "  Organization ID: $ORG_ID"
echo "  Project ID: $PROJECT_ID"
echo "  Host Catalog ID: $HOST_CATALOG_ID"
echo "  Host Set ID: $HOST_SET_ID"
echo "  PostgreSQL Host ID: $POSTGRES_HOST_ID"
echo "  Vault Host ID: $VAULT_HOST_ID"
echo "  PostgreSQL Target ID: $POSTGRES_TARGET_ID"
echo "  Vault Target ID: $VAULT_TARGET_ID"
echo "  User ID: $USER_ID"
echo "  Group ID: $GROUP_ID"
echo "  Role ID: $ROLE_ID"
echo ""
echo "🌐 Access Boundary Admin UI: http://localhost:9200"
echo "   Username: admin"
echo "   Password: password"
echo ""
echo "💡 Connect to PostgreSQL through Boundary:"
echo "   boundary connect postgres -target-id=$POSTGRES_TARGET_ID"
