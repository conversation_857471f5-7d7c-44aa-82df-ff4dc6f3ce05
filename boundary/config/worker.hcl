# Boundary Worker Configuration

# Disable memory lock for development
disable_mlock = true

# Worker configuration block
worker {
  # The name attr is used for operational purposes and can be any string
  name = "demo-worker-1"
  description = "A default worker created for demonstration"

  # Workers must be able to reach controllers on :9201
  controllers = ["boundary-controller:9201"]

  public_addr = "boundary-worker"
}

# Listener for incoming connections from boundary controllers
listener "tcp" {
  address = "0.0.0.0:9202"
  purpose = "proxy"

  tls_disable = true
}

# Worker authorization KMS
# Use a production KMS such as AWS KMS in production installs
kms "aead" {
  purpose = "worker-auth"
  aead_type = "aes-gcm"
  key = "8fZBjCUfN0TzjEGLQldGY4+iE9AkOvCfjh7+p0GtRBQ="
  key_id = "global_worker-auth"
}

# Events (logging) configuration
events {
  audit_enabled       = true
  sysevents_enabled   = true
  observations_enable = true
  sink "stderr" {
    name = "all-events"
    description = "All events sent to stderr"
    event_types = ["*"]
    format = "cloudevents-json"
  }
  sink {
    name = "file-sink"
    description = "All events sent to a file"
    event_types = ["*"]
    format = "cloudevents-json"
    file {
      path = "/boundary/data"
      file_name = "worker-events.ndjson"
    }
    audit_config {
      audit_filter_overrides {
        sensitive = "redact"
        secret    = "redact"
      }
    }
  }
}
