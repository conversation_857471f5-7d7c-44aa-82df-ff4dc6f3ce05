# Custom queries for postgres-exporter to scrape all pg_stat tables
# This file should be mounted to the postgres-exporter container

pg_stat_database:
  query: |
    SELECT 
      datname,
      numbackends,
      xact_commit,
      xact_rollback,
      blks_read,
      blks_hit,
      tup_returned,
      tup_fetched,
      tup_inserted,
      tup_updated,
      tup_deleted,
      conflicts,
      temp_files,
      temp_bytes,
      deadlocks,
      checksum_failures,
      checksum_last_failure,
      blk_read_time,
      blk_write_time,
      session_time,
      active_time,
      idle_in_transaction_time,
      sessions,
      sessions_abandoned,
      sessions_fatal,
      sessions_killed
    FROM pg_stat_database
    WHERE datname NOT IN ('template0', 'template1')
  metrics:
    - datname:
        usage: "LABEL"
        description: "Database name"
    - numbackends:
        usage: "GAUGE"
        description: "Number of backends currently connected to this database"
    - xact_commit:
        usage: "COUNTER"
        description: "Number of transactions committed"
    - xact_rollback:
        usage: "COUNTER"
        description: "Number of transactions rolled back"
    - blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read"
    - blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits"
    - tup_returned:
        usage: "COUNTER"
        description: "Number of rows returned by queries"
    - tup_fetched:
        usage: "COUNTER"
        description: "Number of rows fetched by queries"
    - tup_inserted:
        usage: "COUNTER"
        description: "Number of rows inserted"
    - tup_updated:
        usage: "COUNTER"
        description: "Number of rows updated"
    - tup_deleted:
        usage: "COUNTER"
        description: "Number of rows deleted"
    - conflicts:
        usage: "COUNTER"
        description: "Number of queries canceled due to conflicts"
    - temp_files:
        usage: "COUNTER"
        description: "Number of temporary files created"
    - temp_bytes:
        usage: "COUNTER"
        description: "Total amount of data written to temporary files"
    - deadlocks:
        usage: "COUNTER"
        description: "Number of deadlocks detected"
    - checksum_failures:
        usage: "COUNTER"
        description: "Number of data page checksum failures"
    - blk_read_time:
        usage: "COUNTER"
        description: "Time spent reading data file blocks"
    - blk_write_time:
        usage: "COUNTER"
        description: "Time spent writing data file blocks"
    - session_time:
        usage: "COUNTER"
        description: "Time spent by database sessions"
    - active_time:
        usage: "COUNTER"
        description: "Time spent executing SQL statements"
    - idle_in_transaction_time:
        usage: "COUNTER"
        description: "Time spent idle in transaction"
    - sessions:
        usage: "COUNTER"
        description: "Total number of sessions established"
    - sessions_abandoned:
        usage: "COUNTER"
        description: "Number of database sessions abandoned"
    - sessions_fatal:
        usage: "COUNTER"
        description: "Number of database sessions terminated by fatal errors"
    - sessions_killed:
        usage: "COUNTER"
        description: "Number of database sessions terminated by operator intervention"

pg_stat_user_tables:
  query: |
    SELECT 
      schemaname,
      relname,
      seq_scan,
      seq_tup_read,
      idx_scan,
      idx_tup_fetch,
      n_tup_ins,
      n_tup_upd,
      n_tup_del,
      n_tup_hot_upd,
      n_live_tup,
      n_dead_tup,
      n_mod_since_analyze,
      n_ins_since_vacuum,
      last_vacuum,
      last_autovacuum,
      last_analyze,
      last_autoanalyze,
      vacuum_count,
      autovacuum_count,
      analyze_count,
      autoanalyze_count
    FROM pg_stat_user_tables
  metrics:
    - schemaname:
        usage: "LABEL"
        description: "Schema name"
    - relname:
        usage: "LABEL"
        description: "Table name"
    - seq_scan:
        usage: "COUNTER"
        description: "Number of sequential scans initiated"
    - seq_tup_read:
        usage: "COUNTER"
        description: "Number of live rows fetched by sequential scans"
    - idx_scan:
        usage: "COUNTER"
        description: "Number of index scans initiated"
    - idx_tup_fetch:
        usage: "COUNTER"
        description: "Number of live rows fetched by index scans"
    - n_tup_ins:
        usage: "COUNTER"
        description: "Number of rows inserted"
    - n_tup_upd:
        usage: "COUNTER"
        description: "Number of rows updated"
    - n_tup_del:
        usage: "COUNTER"
        description: "Number of rows deleted"
    - n_tup_hot_upd:
        usage: "COUNTER"
        description: "Number of rows HOT updated"
    - n_live_tup:
        usage: "GAUGE"
        description: "Estimated number of live rows"
    - n_dead_tup:
        usage: "GAUGE"
        description: "Estimated number of dead rows"
    - n_mod_since_analyze:
        usage: "GAUGE"
        description: "Number of rows modified since last analyze"
    - n_ins_since_vacuum:
        usage: "GAUGE"
        description: "Number of rows inserted since last vacuum"
    - vacuum_count:
        usage: "COUNTER"
        description: "Number of times this table has been manually vacuumed"
    - autovacuum_count:
        usage: "COUNTER"
        description: "Number of times this table has been vacuumed by autovacuum"
    - analyze_count:
        usage: "COUNTER"
        description: "Number of times this table has been manually analyzed"
    - autoanalyze_count:
        usage: "COUNTER"
        description: "Number of times this table has been analyzed by autoanalyze"

pg_stat_user_indexes:
  query: |
    SELECT 
      schemaname,
      relname,
      indexrelname,
      idx_scan,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes
  metrics:
    - schemaname:
        usage: "LABEL"
        description: "Schema name"
    - relname:
        usage: "LABEL"
        description: "Table name"
    - indexrelname:
        usage: "LABEL"
        description: "Index name"
    - idx_scan:
        usage: "COUNTER"
        description: "Number of index scans initiated on this index"
    - idx_tup_read:
        usage: "COUNTER"
        description: "Number of index entries returned by scans"
    - idx_tup_fetch:
        usage: "COUNTER"
        description: "Number of live table rows fetched by simple index scans"

pg_stat_activity:
  query: |
    SELECT 
      state,
      COUNT(*) as count,
      COUNT(*) FILTER (WHERE state = 'active') as active_connections,
      COUNT(*) FILTER (WHERE state = 'idle') as idle_connections,
      COUNT(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction_connections,
      COUNT(*) FILTER (WHERE state = 'idle in transaction (aborted)') as idle_in_transaction_aborted_connections
    FROM pg_stat_activity
    WHERE pid <> pg_backend_pid()
    GROUP BY state
  metrics:
    - state:
        usage: "LABEL"
        description: "Connection state"
    - count:
        usage: "GAUGE"
        description: "Number of connections in this state"
    - active_connections:
        usage: "GAUGE"
        description: "Number of active connections"
    - idle_connections:
        usage: "GAUGE"
        description: "Number of idle connections"
    - idle_in_transaction_connections:
        usage: "GAUGE"
        description: "Number of idle in transaction connections"
    - idle_in_transaction_aborted_connections:
        usage: "GAUGE"
        description: "Number of idle in transaction (aborted) connections"

pg_stat_bgwriter:
  query: |
    SELECT 
      checkpoints_timed,
      checkpoints_req,
      checkpoint_write_time,
      checkpoint_sync_time,
      buffers_checkpoint,
      buffers_clean,
      maxwritten_clean,
      buffers_backend,
      buffers_backend_fsync,
      buffers_alloc
    FROM pg_stat_bgwriter
  metrics:
    - checkpoints_timed:
        usage: "COUNTER"
        description: "Number of scheduled checkpoints"
    - checkpoints_req:
        usage: "COUNTER"
        description: "Number of requested checkpoints"
    - checkpoint_write_time:
        usage: "COUNTER"
        description: "Time spent writing files to disk during checkpoints"
    - checkpoint_sync_time:
        usage: "COUNTER"
        description: "Time spent synchronizing files to disk during checkpoints"
    - buffers_checkpoint:
        usage: "COUNTER"
        description: "Number of buffers written during checkpoints"
    - buffers_clean:
        usage: "COUNTER"
        description: "Number of buffers written by background writer"
    - maxwritten_clean:
        usage: "COUNTER"
        description: "Number of times background writer stopped due to too many buffers"
    - buffers_backend:
        usage: "COUNTER"
        description: "Number of buffers written directly by backends"
    - buffers_backend_fsync:
        usage: "COUNTER"
        description: "Number of times backends executed fsync"
    - buffers_alloc:
        usage: "COUNTER"
        description: "Number of buffers allocated"

pg_stat_archiver:
  query: |
    SELECT
      archived_count,
      last_archived_wal,
      last_archived_time,
      failed_count,
      last_failed_wal,
      last_failed_time
    FROM pg_stat_archiver
  metrics:
    - archived_count:
        usage: "COUNTER"
        description: "Number of WAL files successfully archived"
    - last_archived_wal:
        usage: "LABEL"
        description: "Name of last WAL file successfully archived"
    - failed_count:
        usage: "COUNTER"
        description: "Number of failed attempts for archiving WAL files"
    - last_failed_wal:
        usage: "LABEL"
        description: "Name of WAL file of last failed archival operation"

pg_stat_replication:
  query: |
    SELECT
      application_name,
      client_addr,
      state,
      sent_lsn,
      write_lsn,
      flush_lsn,
      replay_lsn,
      write_lag,
      flush_lag,
      replay_lag,
      sync_priority,
      sync_state
    FROM pg_stat_replication
  metrics:
    - application_name:
        usage: "LABEL"
        description: "Name of the application connected to this WAL sender"
    - client_addr:
        usage: "LABEL"
        description: "IP address of the client connected to this WAL sender"
    - state:
        usage: "LABEL"
        description: "Current WAL sender state"
    - sync_priority:
        usage: "GAUGE"
        description: "Priority of this standby server for being chosen as synchronous standby"
    - sync_state:
        usage: "LABEL"
        description: "Synchronous state of this standby server"

pg_statio_user_tables:
  query: |
    SELECT
      schemaname,
      relname,
      heap_blks_read,
      heap_blks_hit,
      idx_blks_read,
      idx_blks_hit,
      toast_blks_read,
      toast_blks_hit,
      tidx_blks_read,
      tidx_blks_hit
    FROM pg_statio_user_tables
  metrics:
    - schemaname:
        usage: "LABEL"
        description: "Schema name"
    - relname:
        usage: "LABEL"
        description: "Table name"
    - heap_blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read from this table"
    - heap_blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits in this table"
    - idx_blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read from all indexes on this table"
    - idx_blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits in all indexes on this table"
    - toast_blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read from this table's TOAST table"
    - toast_blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits in this table's TOAST table"
    - tidx_blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read from this table's TOAST table indexes"
    - tidx_blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits in this table's TOAST table indexes"

pg_statio_user_indexes:
  query: |
    SELECT
      schemaname,
      relname,
      indexrelname,
      idx_blks_read,
      idx_blks_hit
    FROM pg_statio_user_indexes
  metrics:
    - schemaname:
        usage: "LABEL"
        description: "Schema name"
    - relname:
        usage: "LABEL"
        description: "Table name"
    - indexrelname:
        usage: "LABEL"
        description: "Index name"
    - idx_blks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read from this index"
    - idx_blks_hit:
        usage: "COUNTER"
        description: "Number of buffer hits in this index"

pg_locks:
  query: |
    SELECT
      mode,
      locktype,
      COUNT(*) as count
    FROM pg_locks
    GROUP BY mode, locktype
  metrics:
    - mode:
        usage: "LABEL"
        description: "Lock mode"
    - locktype:
        usage: "LABEL"
        description: "Type of lockable object"
    - count:
        usage: "GAUGE"
        description: "Number of locks of this type and mode"

pg_stat_ssl:
  query: |
    SELECT
      COUNT(*) as ssl_connections,
      COUNT(*) FILTER (WHERE ssl = true) as ssl_enabled_connections,
      COUNT(*) FILTER (WHERE ssl = false) as ssl_disabled_connections
    FROM pg_stat_ssl
  metrics:
    - ssl_connections:
        usage: "GAUGE"
        description: "Total number of connections"
    - ssl_enabled_connections:
        usage: "GAUGE"
        description: "Number of connections with SSL enabled"
    - ssl_disabled_connections:
        usage: "GAUGE"
        description: "Number of connections with SSL disabled"
