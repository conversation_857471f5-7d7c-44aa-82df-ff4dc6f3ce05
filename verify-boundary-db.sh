#!/bin/bash

echo "🔍 Verifying Boundary Database Setup..."
echo

# Check if PostgreSQL is running
echo "📋 Checking PostgreSQL service..."
docker compose ps postgres

echo
echo "🐘 Testing PostgreSQL connection..."
docker compose exec -T postgres psql -U postgres -c "SELECT version();" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL is accessible"
else
    echo "❌ PostgreSQL connection failed"
    exit 1
fi

echo
echo "🗄️  Checking if boundary database exists..."
DB_EXISTS=$(docker compose exec -T postgres psql -U postgres -tAc "SELECT 1 FROM pg_database WHERE datname='boundary';")
if [ "$DB_EXISTS" = "1" ]; then
    echo "✅ Boundary database exists"
else
    echo "❌ Boundary database does not exist"
    exit 1
fi

echo
echo "👤 Checking if boundary_user exists..."
USER_EXISTS=$(docker compose exec -T postgres psql -U postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='boundary_user';")
if [ "$USER_EXISTS" = "1" ]; then
    echo "✅ Boundary user exists"
else
    echo "❌ Boundary user does not exist"
    exit 1
fi

echo
echo "🔐 Testing boundary database connection..."
docker compose exec -T postgres psql -U postgres -d boundary -c "SELECT current_database(), current_user;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Can connect to boundary database"
else
    echo "❌ Cannot connect to boundary database"
    exit 1
fi

echo
echo "📊 Checking boundary database tables (should be empty before init)..."
TABLE_COUNT=$(docker compose exec -T postgres psql -U postgres -d boundary -tAc "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
echo "Number of tables in boundary database: $TABLE_COUNT"

echo
echo "🎯 Testing Boundary controller database URL..."
echo "Database URL: ********************************************/boundary?sslmode=disable"

echo
echo "✅ Boundary database setup verification complete!"
echo
echo "📝 Next steps:"
echo "  1. Start the boundary controller: docker compose up boundary-controller -d"
echo "  2. The controller will automatically initialize the database schema"
echo "  3. Check logs: docker compose logs boundary-controller"
