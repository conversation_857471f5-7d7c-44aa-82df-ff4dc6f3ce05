-- Create boundary database and user
CREATE DATABASE boundary;
CREATE USER boundary_user WITH ENCRYPTED PASSWORD 'boundary_pass';
GRANT ALL PRIVILEGES ON DATABASE boundary TO boundary_user;

-- Connect to boundary database and grant schema privileges
\c boundary;
GRANT ALL ON SCHEMA public TO boundary_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO boundary_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO boundary_user;

-- Ensure the boundary user can create tables
ALTER USER boundary_user CREATEDB;
