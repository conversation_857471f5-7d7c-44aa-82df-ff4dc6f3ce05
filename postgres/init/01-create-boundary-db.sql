-- Create boundary database and user
DO $$
BEGIN
    -- Create boundary database if it doesn't exist
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'boundary') THEN
        CREATE DATABASE boundary;
    END IF;

    -- Create boundary user if it doesn't exist
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'boundary_user') THEN
        CREATE USER boundary_user WITH ENCRYPTED PASSWORD 'boundary_pass';
    END IF;
END
$$;

-- Grant privileges to boundary_user
GRANT ALL PRIVILEGES ON DATABASE boundary TO boundary_user;
ALTER USER boundary_user CREATEDB;

-- Connect to boundary database and grant schema privileges
\c boundary;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO boundary_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO boundary_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO boundary_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO boundary_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO boundary_user;

-- Ensure postgres user can also access boundary database
GRANT ALL PRIVILEGES ON DATABASE boundary TO postgres;
