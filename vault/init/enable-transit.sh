#!/bin/bash

# Script to enable and configure the transit secrets engine in Vault
# This script should be run after Vault is started

set -e

echo "🔐 Configuring HashiCorp Vault..."

# Set Vault address and token
export VAULT_ADDR="http://localhost:8200"
export VAULT_TOKEN="myroot"

# Wait for Vault to be ready
echo "⏳ Waiting for Vault to be ready..."
until vault status >/dev/null 2>&1; do
    echo "Waiting for Vault..."
    sleep 2
done

echo "✅ Vault is ready!"

# Enable the transit secrets engine
echo "🔑 Enabling transit secrets engine..."
vault secrets enable transit || echo "Transit engine may already be enabled"

# Create a named encryption key
echo "🗝️  Creating encryption key 'my-key'..."
vault write -f transit/keys/my-key || echo "Key may already exist"

# Create a key for database encryption
echo "🗝️  Creating database encryption key 'db-key'..."
vault write -f transit/keys/db-key || echo "Key may already exist"

# Create a key for application encryption
echo "🗝️  Creating application encryption key 'app-key'..."
vault write -f transit/keys/app-key || echo "Key may already exist"

# Show available keys
echo "📋 Available encryption keys:"
vault list transit/keys

# Test encryption/decryption
echo "🧪 Testing encryption/decryption..."
PLAINTEXT=$(echo -n "Hello, World!" | base64)
echo "Original text (base64): $PLAINTEXT"

ENCRYPTED=$(vault write -field=ciphertext transit/encrypt/my-key plaintext="$PLAINTEXT")
echo "Encrypted: $ENCRYPTED"

DECRYPTED=$(vault write -field=plaintext transit/decrypt/my-key ciphertext="$ENCRYPTED")
echo "Decrypted (base64): $DECRYPTED"
echo "Decrypted text: $(echo $DECRYPTED | base64 -d)"

echo "✅ Transit secrets engine is configured and working!"

# Create a policy for transit operations
echo "📜 Creating transit policy..."
vault policy write transit-policy - <<EOF
# Allow tokens to encrypt/decrypt using any key in transit
path "transit/encrypt/*" {
  capabilities = ["update"]
}

path "transit/decrypt/*" {
  capabilities = ["update"]
}

path "transit/keys/*" {
  capabilities = ["read", "list"]
}

# Allow listing keys
path "transit/keys" {
  capabilities = ["list"]
}
EOF

# Create a token with transit policy
echo "🎫 Creating token with transit policy..."
TRANSIT_TOKEN=$(vault write -field=token auth/token/create policies="transit-policy" ttl="24h")
echo "Transit token: $TRANSIT_TOKEN"

echo "🎉 Vault setup complete!"
echo ""
echo "📝 Usage examples:"
echo "  export VAULT_ADDR=http://localhost:8200"
echo "  export VAULT_TOKEN=myroot"
echo ""
echo "  # Encrypt data"
echo "  vault write transit/encrypt/my-key plaintext=\$(echo -n 'secret data' | base64)"
echo ""
echo "  # Decrypt data"
echo "  vault write transit/decrypt/my-key ciphertext='vault:v1:...'"
echo ""
echo "  # List keys"
echo "  vault list transit/keys"
