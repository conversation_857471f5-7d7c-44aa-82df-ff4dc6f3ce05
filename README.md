# PostgreSQL Monitoring Stack

This Docker Compose setup provides a complete monitoring stack for PostgreSQL with:
- PostgreSQL database
- Postgres-exporter (with custom queries for all pg_stat tables)
- Prometheus (metrics collection)
- <PERSON><PERSON> (visualization)

## Quick Start

1. Start the stack:
```bash
docker compose up -d
```

2. Access the services:
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **PostgreSQL**: localhost:5432 (postgres/postgres)
- **Postgres-exporter metrics**: http://localhost:9187/metrics

## Services Overview

### PostgreSQL
- **Port**: 5432
- **User**: postgres
- **Password**: postgres
- **Database**: postgres

### Postgres-exporter
- **Port**: 9187
- **Custom queries**: Configured to scrape all pg_stat tables
- **Metrics endpoint**: http://localhost:9187/metrics

### Prometheus
- **Port**: 9090
- **Configuration**: `prometheus/prometheus.yml`
- **Scrapes**: postgres-exporter every 30 seconds

### Grafana
- **Port**: 3000
- **Default login**: admin/admin
- **Data source**: Pre-configured to use Prometheus

## Monitored PostgreSQL Statistics

The postgres-exporter is configured to collect metrics from these pg_stat tables:

### Database Level
- `pg_stat_database` - Database-wide statistics
- `pg_stat_bgwriter` - Background writer statistics
- `pg_stat_archiver` - WAL archiver statistics

### Table Level
- `pg_stat_user_tables` - User table statistics
- `pg_statio_user_tables` - User table I/O statistics

### Index Level
- `pg_stat_user_indexes` - User index statistics
- `pg_statio_user_indexes` - User index I/O statistics

### Connection Level
- `pg_stat_activity` - Active connections and their states
- `pg_stat_ssl` - SSL connection statistics

### Replication
- `pg_stat_replication` - Replication statistics

### Locks
- `pg_locks` - Lock information

## Setting up Grafana Dashboards

1. Login to Grafana at http://localhost:3000
2. Add Prometheus as a data source:
   - URL: `http://prometheus:9090`
   - Access: Server (default)
3. Import PostgreSQL dashboards:
   - Dashboard ID: 9628 (PostgreSQL Database)
   - Dashboard ID: 455 (PostgreSQL Overview)
   - Or create custom dashboards using the available metrics

## Available Metrics

The setup exposes comprehensive PostgreSQL metrics including:

- **Connection metrics**: Active, idle, and idle-in-transaction connections
- **Database activity**: Transactions, queries, tuple operations
- **I/O statistics**: Block reads/hits, buffer usage
- **Table statistics**: Sequential scans, index usage, vacuum/analyze stats
- **Index usage**: Scan counts, tuple reads/fetches
- **Background processes**: Checkpoints, WAL archiving
- **Replication**: Lag, state, synchronization
- **Locks**: Lock types and modes
- **SSL**: Connection encryption status

## Customization

### Adding Custom Queries
Edit `postgres-exporter/queries.yaml` to add custom PostgreSQL queries.

### Modifying Scrape Intervals
Edit `prometheus/prometheus.yml` to change scraping intervals or add new targets.

### Environment Variables
Modify `docker-compose.yml` to change database credentials or other settings.

## Stopping the Stack

```bash
docker compose down
```

To remove volumes (data will be lost):
```bash
docker compose down -v
```

## Troubleshooting

1. **Check service logs**:
```bash
docker compose logs [service-name]
```

2. **Verify postgres-exporter metrics**:
```bash
curl http://localhost:9187/metrics
```

3. **Check Prometheus targets**:
Visit http://localhost:9090/targets

4. **Verify Grafana data source**:
Go to Configuration > Data Sources in Grafana
