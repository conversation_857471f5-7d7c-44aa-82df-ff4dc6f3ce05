global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    params:
      # Enable all pg_stat tables
      collect[]:
        - 'pg_stat_database'
        - 'pg_stat_user_tables'
        - 'pg_stat_user_indexes'
        - 'pg_stat_activity'
        - 'pg_stat_replication'
        - 'pg_stat_bgwriter'
        - 'pg_stat_archiver'
        - 'pg_stat_wal_receiver'
        - 'pg_stat_subscription'
        - 'pg_stat_ssl'
        - 'pg_stat_progress_vacuum'
        - 'pg_stat_progress_cluster'
        - 'pg_stat_progress_create_index'
        - 'pg_stat_progress_analyze'
        - 'pg_stat_progress_basebackup'
        - 'pg_stat_progress_copy'
        - 'pg_statio_user_tables'
        - 'pg_statio_user_indexes'
        - 'pg_statio_user_sequences'
        - 'pg_locks'
        - 'pg_settings'