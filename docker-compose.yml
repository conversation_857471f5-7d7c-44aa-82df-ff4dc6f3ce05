services:
  vault:
    image: hashicorp/vault:latest
    ports:
      - "8200:8200"
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: myroot
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
      VAULT_ADDR: http://0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault_data:/vault/data
      - vault_logs:/vault/logs
      - ./vault/config:/vault/config:ro
      - ./vault/init:/vault/init:ro
    command: vault server -dev -dev-root-token-id=myroot -dev-listen-address=0.0.0.0:8200

  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d:ro

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter
    environment:
      DATA_SOURCE_NAME: "********************************************/postgres?sslmode=disable"
      PG_EXPORTER_EXTEND_QUERY_PATH: "/etc/postgres_exporter/queries.yaml"
      PG_EXPORTER_DISABLE_DEFAULT_METRICS: "false"
      PG_EXPORTER_DISABLE_SETTINGS_METRICS: "false"
    ports:
      - "9187:9187"
    volumes:
      - ./postgres-exporter/queries.yaml:/etc/postgres_exporter/queries.yaml:ro
    depends_on:
      - postgres

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus

  boundary-controller:
    image: hashicorp/boundary:latest
    ports:
      - "9200:9200"
      - "9201:9201"
    environment:
      BOUNDARY_POSTGRES_URL: "********************************************/boundary?sslmode=disable"
    volumes:
      - ./boundary/config:/boundary/config:ro
      - boundary_data:/boundary/data
    depends_on:
      - postgres
    command: |
      sh -c "
        boundary database init -config /boundary/config/controller.hcl || true &&
        boundary server -config /boundary/config/controller.hcl
      "

  boundary-worker:
    image: hashicorp/boundary:latest
    ports:
      - "9202:9202"
    volumes:
      - ./boundary/config:/boundary/config:ro
      - boundary_data:/boundary/data
    depends_on:
      - boundary-controller
    command: boundary server -config /boundary/config/worker.hcl

volumes:
  postgres_data:
  prometheus_data:
  grafana_data:
  vault_data:
  vault_logs:
  boundary_data: