#!/bin/bash

echo "🔍 Verifying PostgreSQL Monitoring Stack..."
echo

# Check if Docker Compose is running
echo "📋 Checking Docker Compose services..."
docker compose ps

echo
echo "🐘 Testing PostgreSQL connection..."
docker compose exec -T postgres psql -U postgres -d postgres -c "SELECT version();" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL is accessible"
else
    echo "❌ PostgreSQL connection failed"
fi

echo
echo "📊 Testing Postgres-exporter metrics..."
curl -s http://localhost:9187/metrics | head -5
if [ $? -eq 0 ]; then
    echo "✅ Postgres-exporter is serving metrics"
else
    echo "❌ Postgres-exporter metrics not accessible"
fi

echo
echo "🎯 Testing Prometheus..."
curl -s http://localhost:9090/-/healthy
if [ $? -eq 0 ]; then
    echo "✅ Prometheus is healthy"
else
    echo "❌ Prometheus not accessible"
fi

echo
echo "📈 Testing Grafana..."
curl -s http://localhost:3000/api/health | grep -q "ok"
if [ $? -eq 0 ]; then
    echo "✅ Grafana is healthy"
else
    echo "❌ Grafana not accessible"
fi

echo
echo "🔗 Service URLs:"
echo "  - Grafana: http://localhost:3000 (admin/admin)"
echo "  - Prometheus: http://localhost:9090"
echo "  - Postgres-exporter: http://localhost:9187/metrics"
echo "  - PostgreSQL: localhost:5432 (postgres/postgres)"

echo
echo "📝 To see available PostgreSQL metrics:"
echo "  curl http://localhost:9187/metrics | grep pg_"
